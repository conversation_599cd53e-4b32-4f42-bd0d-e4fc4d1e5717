package com.ctrip.corp.bff.hotel.book.mapper.mapperofapprovaldetailsearch

import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval
import com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalDateRange
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.SSOInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7
import com.ctrip.corp.bff.hotel.book.contract.ApprovalBaseInfoInput
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchRequestType
import com.ctrip.corp.bff.profile.contract.SSOExtendInfo
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType
import com.ctrip.corp.bff.specific.contract.ApprovalInfo
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultResponseType
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.CityInfo
import com.ctrip.corp.foundation.common.exception.TimeZoneNotFoundException
import com.ctrip.corp.foundation.common.plus.time.HotelTimeZoneUtil
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity
import com.ctrip.corp.hotel.book.query.entity.TimeZoneType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

import java.time.LocalDate

/**
 * <AUTHOR>
 * @date 2024-12-11
 * */
class MapperOfApprovalDetailSearchResponseTest extends Specification {

    def mapper = new MapperOfApprovalDetailSearchResponseType()

    def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpID", userId: "userId"))
    def accountInfo = Mock(WrapperOfAccount.AccountInfo.class)
    def searchApprovalInfo = Mock(WrapperOfSearchApproval.ApprovalInfo.class)
    def savePoint = new SavePoint()

    void setup() {
        accountInfo.isOaApprovalHead() >> true
        accountInfo.canEmergencyBook() >> true
        accountInfo.canOnlyQueryPrice() >> true
        searchApprovalInfo.checkInBeginDateAfterFloatDay() >> 1
        searchApprovalInfo.checkInEndDateAfterFloatDay() >> 1
        searchApprovalInfo.checkOutBeginDateAfterFloatDay() >> 1
        searchApprovalInfo.checkInBeginDateAfterFloatDay() >> 1
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert"() {
        given:
        def request = new ApprovalDetailSearchRequestType(integrationSoaRequestType: integrationSoaRequestType)
        request.setCorpPayInfo(new CorpPayInfo("public"))
        def responseType = new BatchApprovalDefaultResponseType()
        def wrapper = new WrapperOfSearchApproval()
        def approvalInfo = wrapper.new WrapperOfSearchApproval.ApprovalInfo()
        def textInfoResponseType = new ApprovalTextInfoResponseType()
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()
        def param = Tuple7.of(
                request, responseType, accountInfo,  approvalInfo, textInfoResponseType, ssoInfoQueryResponseType, new HashMap<>())

        new MockUp<WrapperOfSearchApproval.ApprovalInfo>() {
            @Mock
            public List<CityInfo> getCheckInCityInfos() {
                return new ArrayList<>();
            }
        }

        when:
        def result = mapper.convert(param)

        then:
        result != null
    }

    def "check"() {
        given:
        def responseType = new BatchApprovalDefaultResponseType()
        def param = Tuple7.of(null, responseType, null, null, null, null, null)

        when:
        def result = mapper.check(param)

        then:
        result == null
    }

    def "isIgnoreApprovalNo"() {
        given:
        def request = new ApprovalDetailSearchRequestType()
        request.setCorpPayInfo(new CorpPayInfo("public"))
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()
        def approvalInfo = new ApprovalInfo()

        when:
        def result = mapper.isIgnoreApprovalNo(request, accountInfo, ssoInfoQueryResponseType, approvalInfo)

        then:
        result == false
    }

    def "buildApprovalOutPut"() {
        given:
        def approvalInfo = new ApprovalInfo()
        def corpId = "corpId"
        def searchApprovalInfo = new WrapperOfSearchApproval.ApprovalInfo()
        def textInfoResponseType = new ApprovalTextInfoResponseType()

        when:
        def result = mapper.buildApprovalOutPut(approvalInfo, corpId, searchApprovalInfo, textInfoResponseType, accountInfo, null)

        then:
        result != null
    }

    def "buildApprovalControl"() {
        given:
        def corpId = "corpId"

        when:
        def result = mapper.buildApprovalControl(corpId, searchApprovalInfo, null)

        then:
        result != null
    }

    def "buildCheckInApprovalDateRange"() {
        given:

        when:
        def result = mapper.buildCheckInApprovalDateRange(searchApprovalInfo)

        then:
        result != null
    }

    def "buildCheckOutApprovalDateRange"() {
        given:
        def corpId = "corpId"

        when:
        def result = mapper.buildCheckOutApprovalDateRange(searchApprovalInfo, corpId)

        then:
        result != null
    }

    def "buildDefaultApprovalDateRange"() {
        given:
        def approvalInfo = new ApprovalInfo()
        def corpId = "corpId"
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()

        when:
        def result = mapper.buildDefaultApprovalDateRange(approvalInfo, corpId, ssoInfoQueryResponseType, null)

        then:
        result != null
    }

    def "getBooingDateSpecial"() {
        given:
        def approvalInfo = new ApprovalInfo()

        when:
        def result = mapper.getBooingDateSpecial(approvalInfo, null)

        then:
        result != null
    }

    def "getBooingDateForApproval"() {
        given:
        def approvalInfo = new ApprovalInfo()

        when:
        def result = mapper.getBooingDateForApproval(approvalInfo)

        then:
        result != null
    }

    def "getBooingDateCommon"() {
        given:
        def approvalInfo = new ApprovalInfo()

        when:
        def result = mapper.getBooingDateCommon(approvalInfo, null)

        then:
        result != null
    }

    def "getEndDate"() {
        given:
        def startDate = LocalDate.now()
        def checkInEndDate = "2024-12-31"
        def checkOutStartDate = "2024-12-01"
        def checkOutEndDate = "2024-12-31"

        when:
        def result = mapper.getEndDate(startDate, checkInEndDate, checkOutStartDate, checkOutEndDate)

        then:
        result != null
    }

    def "parseToEndTime"() {
        given:
        def startDate = LocalDate.now()
        def checkEndTime = "2024-12-31"

        when:
        def result = mapper.parseToEndTime(startDate, checkEndTime)

        then:
        result != null
    }

    def "buildApprovalDateRangeBySSO"() {
        given:
        def approvalDateRange = new ApprovalDateRange()
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()

        when:
        def result = mapper.buildApprovalDateRangeBySSO(approvalDateRange, ssoInfoQueryResponseType)

        then:
        result != null
    }

    def "buildApprovalDateRangeBySSO with different scenarios"() {
        given:
        def mapper = new MapperOfApprovalDetailSearchResponseType()
        def approvalDateRange = new ApprovalDateRange(beginDate: beginDate, endDate: endDate)
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()
        Map<String, String> extendInfo = new HashMap<>()
        extendInfo.put(MapperOfApprovalDetailSearchResponseType.CHECK_IN_DATE, ssoCheckInDate)
        extendInfo.put(MapperOfApprovalDetailSearchResponseType.CHECK_OUT_DATE, ssoCheckOutDate)
        ssoInfoQueryResponseType.setSsoExtendInfoList([new SSOExtendInfo(extendInfo: extendInfo, productType: "Hotel")])

        when:
        def result = mapper.buildApprovalDateRangeBySSO(approvalDateRange, ssoInfoQueryResponseType)

        then:
        result.beginDate == expectedBeginDate
        result.endDate == expectedEndDate

        where:
        beginDate     | endDate       | ssoCheckInDate | ssoCheckOutDate || expectedBeginDate | expectedEndDate
        "2024-01-01"  | "2024-01-10"  | "2024-01-05"   | "2024-01-15"    || "2024-01-05"      | "2024-01-10"
        "2024-01-01"  | "2024-01-10"  | "2023-12-25"   | "2024-01-05"    || "2024-01-01"      | "2024-01-05"
        "2024-01-01"  | "2024-01-10"  | null           | "2024-01-15"    || "2024-01-01"      | "2024-01-10"
        "2024-01-01"  | "2024-01-10"  | "2024-01-05"   | null            || "2024-01-05"      | "2024-01-10"
        null          | "2024-01-10"  | "2024-01-05"   | "2024-01-15"    || "2024-01-05"      | "2024-01-10"
        "2024-01-01"  | null          | "2024-01-05"   | "2024-01-15"    || "2024-01-05"      | "2024-01-15"
        null          | null          | "2024-01-05"   | "2024-01-15"    || "2024-01-05"      | "2024-01-15"
        "2024-01-01"  | "2024-01-10"  | null           | null            || "2024-01-01"      | "2024-01-10"
    }

    def "hasDateIntersection"() {
        given:
        def startDate1 = LocalDate.now()
        def endDate1 = LocalDate.now().plusDays(1)
        def startDate2 = LocalDate.now()
        def endDate2 = LocalDate.now().plusDays(1)

        when:
        def result = mapper.hasDateIntersection(startDate1, endDate1, startDate2, endDate2)

        then:
        result == true
    }

    def "buildLongRent"() {
        given:
        def approvalDateRange = new ApprovalDateRange()
        def corpId = "corpId"
        def oversea = "oversea"

        when:
        def result = mapper.buildLongRent(approvalDateRange, corpId, oversea)

        then:
        result != null
    }

    def "buildDateRangeLimit"() {
        given:
        def approvalDateRange = new ApprovalDateRange()

        when:
        def result = mapper.buildDateRangeLimit(approvalDateRange, null)

        then:
        result != null
    }

    def "buildApprovalSwitchInfo"() {
        given:
        def approvalInfo = new ApprovalInfo()
        def approvalDetailSearchRequest = new ApprovalDetailSearchRequestType()
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()

        when:
        def result = mapper.buildApprovalSwitchInfo(approvalInfo, approvalDetailSearchRequest, ssoInfoQueryResponseType)

        then:
        result != null
    }

    def "buildApprovalCityInfo"() {
        given:
        def cityInfo = new CityInfo()

        when:
        def result = mapper.buildApprovalCityInfo(cityInfo, null)

        then:
        result == null
    }

    def "buildApprovalCityInfo returns null when cityInfo is null"() {
        when:
        def result = mapper.buildApprovalCityInfo(null, new HashMap<String, String>())

        then:
        result == null
    }

    def "buildApprovalCityInfo returns null when cityId is null"() {
        given:
        def cityInfo = new CityInfo(cityId: null)

        when:
        def result = mapper.buildApprovalCityInfo(cityInfo, new HashMap<String, String>())

        then:
        result == null
    }

    def "buildApprovalCityInfo returns null when cityId is invalid"() {
        given:
        def cityInfo = new CityInfo(cityId: "-1")

        when:
        def result = mapper.buildApprovalCityInfo(cityInfo, new HashMap<String, String>())

        then:
        result == null
    }

    def "buildApprovalCityInfo returns correct city info for valid cityId"() {
        given:
        def cityInfo = new CityInfo(cityId: "1", cityName: "Test City")
        def cityOffsetMap = ["1" : "480"]

        new MockUp<CityInfoUtil>() {
            @Mock
            boolean oversea(Integer cityId) {
                return false
            }
        }

        when:
        def result = mapper.buildApprovalCityInfo(cityInfo, cityOffsetMap)

        then:
        result != null
        result.cityId == "1"
        result.cityName == "Test City"
        result.cityType == "DOMESTIC"
        result.offset == "480"
    }

    def "buildApprovalCityInfo handles TimeZoneNotFoundException"() {
        given:
        def cityInfo = new CityInfo(cityId: "1", cityName: "Test City")
        def cityOffsetMap = ["1" : ""]

        new MockUp<CityInfoUtil>() {
            @Mock
            boolean oversea(Integer cityId) {
                return true
            }
        }

        when:
        def result = mapper.buildApprovalCityInfo(cityInfo, cityOffsetMap)

        then:
        result != null
        result.cityId == "1"
        result.cityName == "Test City"
        result.cityType == "OVERSEA"
        result.offset == null
    }

    def "buildDefaultApprovalCityInfo_null"() {
        given:
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()

        when:
        def result = mapper.buildDefaultApprovalCityInfo(null, ssoInfoQueryResponseType, null)

        then:
        result == null
    }

    def "buildDefaultApprovalCityInfo"() {
        given:
        new MockUp<CityInfoUtil>() {
            @Mock
            boolean oversea(Integer cityId) {
                return false
            }
        }
        def wrapper = new WrapperOfSearchApproval();
        def approvalInfo = wrapper.new WrapperOfSearchApproval.ApprovalInfo()
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()
        ssoInfoQueryResponseType.setSsoExtendInfoList([new SSOExtendInfo(extendInfo: ["cityId": "1"])])
        new MockUp<WrapperOfSearchApproval.ApprovalInfo>() {
            @Mock
            public List<CityInfo> getCheckInCityInfos() {
                return Arrays.asList(
                        new CityInfo(cityId: 0, cityName: "Invalid City"),
                        new CityInfo(cityId: -1, cityName: "Invalid City"),
                        new CityInfo(cityId: 1, cityName: "Test City"),
                )
            }
        }

        def cityOffsetMap = ["1" : "480"]


        when:
        def result = mapper.buildDefaultApprovalCityInfo(approvalInfo, ssoInfoQueryResponseType, cityOffsetMap)

        then:
        result != null
        result.cityId == "1"
        result.cityName == "Test City"
        result.cityType == "DOMESTIC"
        result.offset == "480"
    }

    def "isSSO"() {
        given:
        def request = new ApprovalDetailSearchRequestType()
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()

        when:
        def result = mapper.isSSO(request, ssoInfoQueryResponseType)

        then:
        result == false
    }

    def "buildCanOnlyQueryPrice"() {
        given:

        when:
        def result = mapper.buildCanOnlyQueryPrice(accountInfo)

        then:
        result == "T"
    }

    def "canEmergencyBook"() {
        given:

        when:
        def result = mapper.canEmergencyBook(accountInfo)

        then:
        result == "T"
    }

    def "isIgnoreApprovalNo with different scenarios"() {
        given:
        def mapper = new MapperOfApprovalDetailSearchResponseType()
        def request = new ApprovalDetailSearchRequestType()
        def accountInfo = Mock(WrapperOfAccount.AccountInfo.class)
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()
        def approvalInfo = new ApprovalInfo()

        request.setSsoInput(new SSOInput(ssoKey: "123"))
        request.setCorpPayInfo(new CorpPayInfo(corpPayInfo))
        accountInfo.bookTravelersMustSameTripApprove(_) >> bookTravelersMustSameTripApprove
        accountInfo.isPreApprovalRequired(_, _) >> preApprovalRequired
        accountInfo.isTravelApplyRequired(_, _) >> travelApplyRequired
        approvalInfo.setDomesticFlag(domesticFlag)
        request.setApprovalBaseInfoInput(approvalBaseInfoInput)
        request.setIntegrationSoaRequestType(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "123")))

        when:
        def result = mapper.isIgnoreApprovalNo(request, accountInfo, ssoInfoQueryResponseType, approvalInfo)

        then:
        result == expected

        where:
        corpPayInfo | bookTravelersMustSameTripApprove | preApprovalRequired | travelApplyRequired | domesticFlag | approvalBaseInfoInput                          || expected
        "private"   | true                             | true                | false               | "T"          | null                                           || false
        "private"   | false                            | false               | false               | "F"          | null                                           || false
        "public"    | true                             | false               | true                | "F"          | null                                           || false
        "public"    | true                             | false               | true                | "F"          | new ApprovalBaseInfoInput(pageType: "PASSAGE") || true
        "public"    | true                             | true                | true                | "T"          | new ApprovalBaseInfoInput(pageType: "OTHER")   || false
    }
}
